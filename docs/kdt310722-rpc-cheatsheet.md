# @kdt310722/rpc Cheatsheet

## Table of Contents

- [Overview](#overview)
- [Installation](#installation)
- [Core Concepts](#core-concepts)
- [API Reference](#api-reference)
- [Usage Examples](#usage-examples)
- [Best Practices](#best-practices)
- [Dependencies](#dependencies)

## Overview

`@kdt310722/rpc` is a high-performance JSON-RPC WebSocket client and server implementation for Node.js applications. It provides a complete solution for building WebSocket-based RPC systems with automatic reconnection, heartbeat monitoring, batch request support, and comprehensive error handling.

**Key Features:**

- JSON-RPC 2.0 compliant client and server implementations
- WebSocket-based communication with auto-reconnection
- Heartbeat/ping-pong monitoring for connection health
- Batch request support for improved performance
- Comprehensive error handling with custom error types
- TypeScript support with full type safety
- Timeout handling for requests and operations
- Event-driven architecture with extensible event system
- High-performance message handling and encoding/decoding

## Installation

```bash
npm install @kdt310722/rpc @kdt310722/utils ws
# or
pnpm add @kdt310722/rpc @kdt310722/utils ws
# or
yarn add @kdt310722/rpc @kdt310722/utils ws
```

**Required Dependencies:**

- `@kdt310722/utils` (>=0.0.17) - Utility functions
- `ws` (>=8.18.1) - WebSocket implementation

**Requirements:**

- Node.js >= 22.14.0

## Core Concepts

### JSON-RPC Message Types

The library implements JSON-RPC 2.0 specification with four message types:

- **Request Message** - Calls a method and expects a response
- **Notification Message** - Calls a method without expecting a response
- **Success Response** - Contains result data from a successful request
- **Error Response** - Contains error information from a failed request

### WebSocket Client-Server Architecture

- **WebSocketClient** - Low-level WebSocket client with reconnection and heartbeat
- **RpcWebSocketClient** - High-level RPC client built on WebSocketClient
- **WebSocketServer** - Low-level WebSocket server with client management
- **RpcWebSocketServer** - High-level RPC server built on WebSocketServer

### Message Handling System

- **RpcMessageHandler** - Core message processing engine for RPC methods
- **Heartbeat** - Connection health monitoring system
- **DataEncoder/DataDecoder** - Message serialization interfaces

## API Reference

### Types and Interfaces

#### Core Types

```typescript
// Basic types
type UrlLike = URL | string
type WebSocketMessage = RawData | string
type JsonRpcResponseId = string | number | null

// Data transformation functions
type DataEncoder = (data: JsonRpcMessage[] | JsonRpcMessage) => WebSocketMessage
type DataDecoder = (data: WebSocketMessage) => JsonRpcMessage | JsonRpcMessage[]

// Method handler for RPC methods
type RpcMethodHandler<TContext = any> = TContext extends void | undefined
    ? (params: any) => Awaitable<any>
    : (params: any, context: TContext) => Awaitable<any>
```

#### Message Interfaces

```typescript
interface BaseJsonRpcMessage {
    jsonrpc: '2.0'
}

interface JsonRpcRequestMessage extends BaseJsonRpcMessage {
    id: string | number
    method: string
    params?: any
}

interface JsonRpcNotifyMessage extends BaseJsonRpcMessage {
    method: string
    params?: any
}

interface JsonRpcErrorObject {
    code: number
    message: string
    data?: any
}

interface JsonRpcSuccessResponseMessage<R = any> extends BaseJsonRpcMessage {
    id: JsonRpcResponseId
    result: R
}

interface JsonRpcErrorResponseMessage extends BaseJsonRpcMessage {
    id: JsonRpcResponseId
    error: JsonRpcErrorObject
}
```

### Error Classes

#### JsonRpcError

Primary error class for JSON-RPC errors with chainable data attachment:

```typescript
class JsonRpcError extends Error {
    public constructor(code: number, message: string, options?: ErrorOptions)

    // Chain additional data to the error
    public withData(data?: any): this

    // Convert to JSON-RPC error object
    public toJSON(): JsonRpcErrorObject

    // Create error response message
    public toResponse(id: JsonRpcResponseId): JsonRpcErrorResponseMessage
}
```

#### JsonRpcRequestError

Specialized error for request failures with contextual information:

```typescript
class JsonRpcRequestError extends Error {
    // Chain URL information
    public withUrl(url?: string): this

    // Chain request payload
    public withPayload(payload?: JsonRpcRequestMessage | JsonRpcRequestMessage[]): this

    // Chain response data
    public withResponse(response?: JsonRpcResponseMessage): this
}
```

#### JsonRpcBatchRequestError

Error class for batch request failures:

```typescript
class JsonRpcBatchRequestError extends Error {
    // Chain batch payloads
    public withPayloads(payloads?: JsonRpcRequestMessage[]): this
}
```

#### WebsocketClientError

WebSocket-specific error with client context:

```typescript
class WebsocketClientError extends Error {
    public readonly url?: string

    public constructor(client?: WebSocketClient, message?: string, options?: ErrorOptions)
}
```

### Utility Functions

#### Message Creation and Validation

```typescript
// Message type guards
function isJsonRpcMessage(message: unknown): message is JsonRpcMessage
function isJsonRpcRequestMessage(message: JsonRpcMessage): message is JsonRpcRequestMessage
function isJsonRpcNotifyMessage(message: JsonRpcMessage): message is JsonRpcNotifyMessage
function isJsonRpcResponseMessage(message: JsonRpcMessage): message is JsonRpcResponseMessage
function isJsonRpcErrorResponseMessage(message: JsonRpcMessage): message is JsonRpcErrorResponseMessage

// Message creation utilities
function createNotifyMessage(method: string, params?: any): JsonRpcNotifyMessage
function createRequestMessage(id: string | number, method: string, params?: any): JsonRpcRequestMessage
function createSuccessResponseMessage<R = any>(id: JsonRpcResponseId, result: R): JsonRpcSuccessResponseMessage<R>
function createErrorResponseMessage(id: JsonRpcResponseId, error: JsonRpcError): JsonRpcErrorResponseMessage

// Error conversion
function toJsonRpcError(error: JsonRpcErrorObject, options?: ErrorOptions): JsonRpcError
```

#### Request Utilities

```typescript
// Extract client IP from HTTP request
function getRequestClientIp(request: IncomingMessage): string
```

### WebSocket Client

#### WebSocketClient

Low-level WebSocket client with reconnection and heartbeat support:

```typescript
interface WebSocketClientOptions {
    protocols?: string | string[]
    connectTimeout?: number        // Default: 10000ms
    disconnectTimeout?: number     // Default: 10000ms
    sendTimeout?: number          // Default: 10000ms
    reconnect?: ReconnectOptions | boolean  // Default: true
    heartbeat?: HeartbeatOptions | boolean  // Default: true
}

interface ReconnectOptions {
    enable?: boolean    // Default: true
    delay?: number     // Default: 1000ms
    attempts?: number  // Default: 3
}

interface HeartbeatOptions {
    enable?: boolean   // Default: true
    interval?: number  // Default: 30000ms
    timeout?: number   // Default: 10000ms
}

class WebSocketClient extends Emitter<WebSocketClientEvents> {
    public readonly url: string
    public readonly protocols: string[]
    public isReconnecting: boolean

    public constructor(url: UrlLike, options?: WebSocketClientOptions)

    // Connection state
    public get isConnected(): boolean
    public get isReconnectAttemptReached(): boolean

    // Connection management
    public async connect(): Promise<void>
    public async disconnect(code?: number | boolean, reason?: string, isExplicitlyClosed?: boolean): Promise<void>
    public terminate(): void
    public resetRetryCount(): void

    // Message sending
    public async send(message: WebSocketMessage): Promise<void>
}

type WebSocketClientEvents = {
    'connected': () => void
    'disconnected': (code: number, reason: Buffer, isExplicitlyClosed: boolean) => void
    'reconnect': (attempts: number) => void
    'error': (error: WebsocketClientError) => void
    'message': (message: WebSocketMessage) => void
}
```

#### RpcWebSocketClient

High-level RPC client built on WebSocketClient:

```typescript
interface RpcWebsocketClientOptions extends WebSocketClientOptions {
    requestTimeout?: number    // Default: 10000ms
    dataEncoder?: DataEncoder  // Default: stringifyJson
    dataDecoder?: DataDecoder  // Default: parseJson
}

interface RequestPayload<R = any> {
    readonly __result?: R  // Type hint for result
    id: string | number
    method: string
    params?: any
}

class RpcWebSocketClient extends Emitter<RpcWebSocketClientEvents> {
    public readonly socket: WebSocketClient

    public constructor(url: UrlLike, options?: RpcWebsocketClientOptions)

    // RPC methods
    public async notify(method: string, params?: any): Promise<void>
    public async call<R = any>(method: string, params?: any, id?: string | number): Promise<R>
    public async batchCall<T extends [RequestPayload, ...RequestPayload[]]>(payloads: T): Promise<{ [K in keyof T]: NonNullable<T[K]['__result']> }>

    // Request payload creation
    public createRequestPayload<R = any>(method: string, params?: any, id?: string | number): RequestPayload<R>
}

type RpcWebSocketClientEvents = {
    error: (error: JsonRpcError) => void
    notification: (method: string, params?: any) => void
    unhandledMessage: (message: WebSocketMessage) => void
    unhandledRpcMessage: (message: JsonRpcMessage) => void
}
```

### WebSocket Server

#### WebSocketServer

Low-level WebSocket server with client management:

```typescript
interface WebSocketServerOptions {
    path?: string                    // Default: '/'
    listener?: RequestListener       // HTTP request listener
    heartbeat?: HeartbeatOptions | boolean  // Default: true
    sendTimeout?: number            // Default: 10000ms
    beforeUpgrade?: BeforeUpgradeHandler
}

interface Client<TMetadata extends AnyObject = AnyObject> {
    id: number
    ip: string
    socket: WebSocket
    request: IncomingMessage
    metadata: TMetadata
    send: (message: WebSocketMessage) => Promise<void>
}

type BeforeUpgradeHandler<TMetadata extends AnyObject = AnyObject> = (
    context: BeforeUpgradeContext<TMetadata>,
    upgrade: () => void
) => void

class WebSocketServer<TMetadata extends AnyObject = AnyObject> extends Emitter<WebSocketServerEvents<TMetadata>> {
    public readonly host: string
    public readonly port: number

    public constructor(host: string, port: number, options?: WebSocketServerOptions)

    // Server lifecycle
    public async start(): Promise<void>
    public async stop(): Promise<void>

    // Message sending
    public async send(socket: WebSocket, message: WebSocketMessage, clientId?: number): Promise<void>
}

type WebSocketServerEvents<TMetadata extends AnyObject = AnyObject> = {
    error: (error: unknown) => void
    clientError: (error: unknown, client: Client<TMetadata>) => void
    listening: () => void
    close: () => void
    connection: (client: Client<TMetadata>) => void
}
```

#### RpcWebSocketServer

High-level RPC server built on WebSocketServer:

```typescript
interface RpcWebSocketServerOptions<TRpcClient extends RpcClient = RpcClient>
    extends WebSocketServerOptions, RpcMessageHandlerOptions {
    methods?: Record<string, RpcMethodHandler<TRpcClient>>
    dataEncoder?: DataEncoder  // Default: stringifyJson
}

interface RpcClient<TMetadata extends AnyObject = AnyObject> {
    id: number
    ip: string
    socket: WebSocket
    request: IncomingMessage
    metadata: TMetadata
    events: Emitter<RpcClientEvents>
    notify: (method: string, params?: any) => Promise<void>
    send: (data: JsonRpcMessage[] | JsonRpcMessage) => Promise<void>
    sendRaw: (data: WebSocketMessage) => Promise<void>
}

class RpcWebSocketServer<TRpcClient extends RpcClient = RpcClient> extends Emitter<RpcWebSocketServerEvents<TRpcClient>> {
    public readonly server: WebSocketServer<TRpcClient['metadata']>
    public readonly messageHandler: RpcMessageHandler

    public constructor(host: string, port: number, options?: RpcWebSocketServerOptions)

    // Method registration
    public addMethod(name: string, handler: RpcMethodHandler<TRpcClient>, override?: boolean): void

    // Message sending
    public async notify(socket: WebSocket, method: string, params?: any, clientId?: number): Promise<void>
    public async send(socket: WebSocket, data: JsonRpcMessage[] | JsonRpcMessage, clientId?: number): Promise<void>
    public getNotifyMessage(method: string, params?: any): WebSocketMessage
}

type RpcWebSocketServerEvents<TRpcClient extends RpcClient = RpcClient> = {
    error: (error: unknown) => void
    connection: (client: TRpcClient) => void
    notification: (client: TRpcClient, method: string, params?: any) => void
    unhandledMessage: (client: TRpcClient, message: WebSocketMessage) => void
}
```

### Message Handler

#### RpcMessageHandler

Core message processing engine for handling RPC methods:

```typescript
interface RpcMessageHandlerOptions {
    errorHandler?: (error: unknown) => any
    operationTimeout?: number  // Default: 60000ms
    maxBatchSize?: number     // Default: 1000
}

class RpcMessageHandler<TContext = any> extends Emitter<RpcMessageHandlerEvents<TContext>, true> {
    public constructor(
        methods: Record<string, RpcMethodHandler<TContext>>,
        options?: RpcMessageHandlerOptions
    )

    // Method management
    public addMethod(name: string, handler: RpcMethodHandler<TContext>, override?: boolean): void

    // Message processing
    public async handleMessage(
        message: RawData,
        context?: TContext
    ): Promise<JsonRpcResponseMessage | JsonRpcResponseMessage[] | typeof RPC_NOTIFY_MESSAGE>
}

type RpcMessageHandlerEvents<TContext = any> = {
    notification: TContext extends void | undefined
        ? (method: string, params: any) => void
        : (method: string, params: any, context: TContext) => void
    error: (error: unknown) => void
    unhandledMessage: TContext extends void | undefined
        ? (message: WebSocketMessage) => void
        : (message: WebSocketMessage, context: TContext) => void
}

// Special symbol returned for notification messages
export const RPC_NOTIFY_MESSAGE: unique symbol
```

### Heartbeat System

#### Heartbeat

Connection health monitoring system:

```typescript
class Heartbeat {
    public constructor(
        timeout: number,    // Timeout for pong response
        interval: number,   // Interval between pings
        fn: Fn,            // Function to call for ping
        onTimeout: Fn      // Function to call on timeout
    )

    public start(): void     // Start heartbeat monitoring
    public stop(): void      // Stop heartbeat monitoring
    public resolve(): void   // Resolve current ping (call on pong)
}
```

## Usage Examples

### Basic RPC Client

```typescript
import { RpcWebSocketClient } from '@kdt310722/rpc'

const client = new RpcWebSocketClient('ws://localhost:8080', {
    requestTimeout: 5000,
    reconnect: { attempts: 5, delay: 2000 },
    heartbeat: { interval: 30000, timeout: 10000 }
})

// Connect to server
await client.connect()

// Call RPC method
const result = await client.call('getUserById', { id: 123 })

// Send notification (no response expected)
await client.notify('logEvent', { event: 'user_login', userId: 123 })

// Batch calls for better performance
const requests = [
    client.createRequestPayload<User>('getUser', { id: 1 }),
    client.createRequestPayload<Profile>('getProfile', { userId: 1 })
]
const [user, profile] = await client.batchCall(requests)

// Handle events
client.on('notification', (method, params) => {
    console.log(`Received notification: ${method}`, params)
})

client.on('error', (error) => {
    console.error('RPC Error:', error)
})
```

### Basic RPC Server

```typescript
import { RpcWebSocketServer } from '@kdt310722/rpc'

const server = new RpcWebSocketServer('localhost', 8080, {
    methods: {
        // Simple method
        echo: (params) => params,

        // Method with context (client info)
        getCurrentUser: (params, client) => {
            return { id: client.id, ip: client.ip, ...params }
        },

        // Async method
        fetchData: async (params) => {
            const data = await database.find(params.query)
            return data
        }
    },

    // Error handling
    errorHandler: (error) => {
        if (error instanceof CustomError) {
            return new JsonRpcError(-32001, error.message).withData(error.details)
        }
        return new JsonRpcError(-32603, 'Internal Error')
    },

    // Timeouts and limits
    operationTimeout: 30000,
    maxBatchSize: 100
})

// Add methods dynamically
server.addMethod('newMethod', (params, client) => {
    return `Hello ${client.ip}: ${params.message}`
})

// Handle events
server.on('connection', (client) => {
    console.log(`Client connected: ${client.id} from ${client.ip}`)

    // Send welcome notification
    client.notify('welcome', { message: 'Connected successfully' })
})

server.on('notification', (client, method, params) => {
    console.log(`Notification from ${client.id}: ${method}`, params)
})

// Start server
await server.start()
console.log('RPC Server running on localhost:8080')
```

### Advanced Client with Custom Encoding

```typescript
import { RpcWebSocketClient } from '@kdt310722/rpc'
import { gzipSync, gunzipSync } from 'node:zlib'

// Custom data encoder/decoder with compression
const client = new RpcWebSocketClient('ws://localhost:8080', {
    dataEncoder: (data) => gzipSync(Buffer.from(JSON.stringify(data))),
    dataDecoder: (data) => JSON.parse(gunzipSync(data as Buffer).toString()),
    requestTimeout: 10000
})

// Type-safe RPC calls
interface UserService {
    getUser: (params: { id: number }) => Promise<User>
    createUser: (params: CreateUserRequest) => Promise<User>
    updateUser: (params: UpdateUserRequest) => Promise<User>
}

class TypedRpcClient {
    constructor(private client: RpcWebSocketClient) {}

    async getUser(id: number): Promise<User> {
        return this.client.call<User>('getUser', { id })
    }

    async createUser(data: CreateUserRequest): Promise<User> {
        return this.client.call<User>('createUser', data)
    }

    async batchGetUsers(ids: number[]): Promise<User[]> {
        const requests = ids.map(id =>
            this.client.createRequestPayload<User>('getUser', { id })
        )
        return this.client.batchCall(requests)
    }
}

const typedClient = new TypedRpcClient(client)
const user = await typedClient.getUser(123)
```

### Server with Client Metadata and Authentication

```typescript
import { RpcWebSocketServer } from '@kdt310722/rpc'
import { JsonRpcError } from '@kdt310722/rpc'

interface ClientMetadata {
    userId?: number
    permissions: string[]
    authenticated: boolean
}

const server = new RpcWebSocketServer<RpcClient<ClientMetadata>>('localhost', 8080, {
    // Authentication before WebSocket upgrade
    beforeUpgrade: ({ request, metadata }, upgrade) => {
        const token = request.headers.authorization?.replace('Bearer ', '')

        if (token && validateToken(token)) {
            const user = decodeToken(token)
            metadata.userId = user.id
            metadata.permissions = user.permissions
            metadata.authenticated = true
            upgrade()
        } else {
            metadata.authenticated = false
            metadata.permissions = []
            upgrade() // Allow connection but with limited access
        }
    },

    methods: {
        // Public method
        ping: () => 'pong',

        // Authenticated method
        getProfile: (params, client) => {
            if (!client.metadata.authenticated) {
                throw new JsonRpcError(-32001, 'Authentication required')
            }
            return getUserProfile(client.metadata.userId!)
        },

        // Permission-based method
        adminAction: (params, client) => {
            if (!client.metadata.permissions.includes('admin')) {
                throw new JsonRpcError(-32002, 'Admin permission required')
            }
            return performAdminAction(params)
        }
    }
})

// Send notifications to specific user groups
server.on('connection', (client) => {
    if (client.metadata.authenticated) {
        // Send personalized welcome
        client.notify('welcome', {
            message: `Welcome back, user ${client.metadata.userId}`
        })

        // Join user-specific notification channel
        subscribeToUserNotifications(client.metadata.userId!, (notification) => {
            client.notify('userNotification', notification)
        })
    }
})
```

### Error Handling Patterns

```typescript
import {
    JsonRpcError,
    JsonRpcRequestError,
    JsonRpcBatchRequestError,
    WebsocketClientError
} from '@kdt310722/rpc'

// Custom error handling in client
client.on('error', (error) => {
    if (error instanceof JsonRpcError) {
        console.error(`RPC Error ${error.code}: ${error.message}`, error.data)
    }
})

// Comprehensive error handling in requests
try {
    const result = await client.call('riskyMethod', params)
} catch (error) {
    if (error instanceof JsonRpcRequestError) {
        console.error('Request failed:', {
            url: error.url,
            payload: error.payload,
            response: error.response
        })
    } else if (error instanceof WebsocketClientError) {
        console.error('WebSocket error:', error.message, 'URL:', error.url)
    }
}

// Server-side error creation with context
server.addMethod('validateData', (params, client) => {
    if (!params.email) {
        throw new JsonRpcError(-32602, 'Invalid params')
            .withData({
                field: 'email',
                message: 'Email is required',
                clientId: client.id
            })
    }

    try {
        return processData(params)
    } catch (error) {
        throw new JsonRpcError(-32603, 'Processing failed', { cause: error })
            .withData({ originalError: error.message })
    }
})
```

## Best Practices

### Connection Management

- **Always handle disconnection events** and implement reconnection logic
- **Use heartbeat monitoring** to detect stale connections early
- **Set appropriate timeouts** based on your network conditions and requirements
- **Handle explicit vs. automatic disconnections** differently

### Error Handling

- **Create specific error codes** for different error types in your application
- **Use JsonRpcError.withData()** to provide additional context
- **Chain error context** using withUrl(), withPayload(), withResponse() methods
- **Handle both synchronous and asynchronous errors** in RPC methods

### Performance Optimization

- **Use batch calls** for multiple related requests to reduce round trips
- **Implement custom data encoders** for compression when dealing with large payloads
- **Set appropriate operation timeouts** to prevent hanging requests
- **Limit batch sizes** to prevent memory issues

### Type Safety

- **Use TypeScript interfaces** to define RPC method signatures
- **Create typed wrappers** around the generic RPC client
- **Define request/response types** for all RPC methods
- **Use RequestPayload<T>** type hints for batch operations

### Security

- **Implement authentication** before WebSocket upgrade using beforeUpgrade handler
- **Validate all input parameters** in RPC method handlers
- **Use client metadata** to store authentication and authorization information
- **Rate limit connections and requests** to prevent abuse

### Monitoring and Logging

- **Log all RPC errors** with appropriate context
- **Monitor connection events** for debugging and analytics
- **Track request/response times** for performance monitoring
- **Use structured logging** with client information

### Resource Management

- **Clean up resources** when clients disconnect
- **Implement proper shutdown procedures** for servers
- **Monitor memory usage** for long-running servers
- **Use connection pooling** for client applications

## Dependencies

This library depends on:

- **@kdt310722/utils** (>=0.0.17) - Provides utility functions for arrays, objects, promises, events, and more
- **ws** (>=8.18.1) - WebSocket implementation for Node.js
- **Node.js** (>=22.14.0) - Runtime environment

The library is designed to be lightweight with minimal dependencies while providing maximum functionality for RPC communication over WebSockets.
