import type { EndpointConfig } from '../app/config/endpoints'
import type { JsonRpcHttpRequestInfo } from '../app/modules/jsonrpc-server/types'
import { Endpoint } from '../app/endpoints/endpoint'
import { EndpointManager } from '../app/endpoints/manager'
import { ParallelStrategy } from '../app/endpoints/strategies/parallel'
import { JsonRpcError, JsonRpcErrorCode } from '../app/errors/json-rpc-error'

// Mock endpoint configuration
const createMockEndpointConfig = (name: string, url: string): EndpointConfig => ({
    name,
    enabled: true,
    http: {
        url,
        timeout: 5000,
        maxRequestsPerSecond: 100,
        maxConcurrentRequests: 10,
    },
})

// Mock endpoint that simulates different response behaviors
class MockEndpoint extends Endpoint {
    private readonly mockBehavior: 'success' | 'error' | 'slow' | 'jsonrpc-error'
    private readonly delay: number

    constructor(config: EndpointConfig, behavior: 'success' | 'error' | 'slow' | 'jsonrpc-error' = 'success', delay = 0) {
        super(config)
        this.mockBehavior = behavior
        this.delay = delay
    }

    public async send(request: any, options?: any) {
        // Simulate delay
        if (this.delay > 0) {
            await new Promise((resolve) => setTimeout(resolve, this.delay))
        }

        // Check if aborted
        if (options?.signal?.aborted) {
            throw new Error('Request aborted')
        }

        const took = BigInt(this.delay * 1_000_000) // Convert ms to ns

        switch (this.mockBehavior) {
            case 'success':
                return {
                    id: '1',
                    status: 200,
                    headers: {},
                    body: {
                        jsonrpc: '2.0' as const,
                        id: request.id,
                        result: { success: true, endpoint: this.name },
                    },
                    took,
                    metadata: {},
                }
            case 'jsonrpc-error':
                return {
                    id: '1',
                    status: 200,
                    headers: {},
                    body: {
                        jsonrpc: '2.0' as const,
                        id: request.id,
                        error: {
                            code: -32_603,
                            message: 'Internal error',
                            data: { endpoint: this.name },
                        },
                    },
                    took,
                    metadata: {},
                }
            case 'error':
                throw new Error(`Network error from ${this.name}`)
            case 'slow':
                // This should be aborted before completing
                await new Promise((resolve) => setTimeout(resolve, 10_000))

                return {
                    id: '1',
                    status: 200,
                    headers: {},
                    body: {
                        jsonrpc: '2.0' as const,
                        id: request.id,
                        result: { success: true, endpoint: this.name },
                    },
                    took,
                    metadata: {},
                }
        }
    }

    public isAvailable() {
        return true
    }
}

// Test function
async function testParallelStrategy() {
    console.log('🧪 Testing ParallelStrategy...\n')

    // Test 1: Multiple endpoints, fastest wins
    console.log('Test 1: Multiple endpoints - fastest should win')

    try {
        const endpoints = [
            new MockEndpoint(createMockEndpointConfig('fast', 'http://fast.example.com'), 'success', 50),
            new MockEndpoint(createMockEndpointConfig('medium', 'http://medium.example.com'), 'success', 100),
            new MockEndpoint(createMockEndpointConfig('slow', 'http://slow.example.com'), 'success', 200),
        ]

        // Create a mock manager that provides the endpoints
        const manager = {
            getEndpoints: () => endpoints,
            hasEndpoint: (name: string) => endpoints.some((e) => e.name === name),
            getEndpointByName: (name: string) => endpoints.find((e) => e.name === name),
        } as any

        const strategy = new ParallelStrategy()
        await strategy.initialize(manager)

        const request: JsonRpcHttpRequestInfo = {
            id: 'test-1',
            message: { jsonrpc: '2.0', id: 1, method: 'test', params: {} },
            headers: {},
            clientIp: '127.0.0.1',
            signal: new AbortController().signal,
            processTime: BigInt(0),
        }

        const response = await strategy.dispatch(request)

        console.log(`✅ Success! Fastest endpoint won: ${response.headers?.['X-Endpoint']}`)
        console.log(`   Response: ${JSON.stringify(response.body)}\n`)
    } catch (error) {
        console.log(`❌ Test 1 failed: ${error}\n`)
    }

    // Test 2: All endpoints fail
    console.log('Test 2: All endpoints fail - should aggregate errors')

    try {
        const endpoints = [
            new MockEndpoint(createMockEndpointConfig('error1', 'http://error1.example.com'), 'error'),
            new MockEndpoint(createMockEndpointConfig('error2', 'http://error2.example.com'), 'jsonrpc-error'),
            new MockEndpoint(createMockEndpointConfig('error3', 'http://error3.example.com'), 'error'),
        ]

        const manager = {
            getEndpoints: () => endpoints,
            hasEndpoint: (name: string) => endpoints.some((e) => e.name === name),
            getEndpointByName: (name: string) => endpoints.find((e) => e.name === name),
        } as any

        const strategy = new ParallelStrategy()
        await strategy.initialize(manager)

        const request: JsonRpcHttpRequestInfo = {
            id: 'test-2',
            message: { jsonrpc: '2.0', id: 2, method: 'test', params: {} },
            headers: {},
            clientIp: '127.0.0.1',
            signal: new AbortController().signal,
            processTime: BigInt(0),
        }

        await strategy.dispatch(request)
        console.log(`❌ Test 2 should have failed but didn't\n`)
    } catch (error) {
        if (error instanceof JsonRpcError && error.code === JsonRpcErrorCode.INTERNAL_ERROR) {
            console.log(`✅ Success! All endpoints failed as expected`)
            console.log(`   Error: ${error.message}`)
            console.log(`   Data: ${JSON.stringify(error.data)}\n`)
        } else {
            console.log(`❌ Test 2 failed with unexpected error: ${error}\n`)
        }
    }

    // Test 3: Mixed success and failure - success should win
    console.log('Test 3: Mixed success and failure - success should win')

    try {
        const endpoints = [
            new MockEndpoint(createMockEndpointConfig('error', 'http://error.example.com'), 'error'),
            new MockEndpoint(createMockEndpointConfig('success', 'http://success.example.com'), 'success', 100),
            new MockEndpoint(createMockEndpointConfig('jsonrpc-error', 'http://jsonrpc-error.example.com'), 'jsonrpc-error'),
        ]

        const manager = new EndpointManager([], [], {});
        (manager as any).endpoints = endpoints

        const strategy = new ParallelStrategy()
        await strategy.initialize(manager)

        const request: JsonRpcHttpRequestInfo = {
            id: 'test-3',
            message: { jsonrpc: '2.0', id: 3, method: 'test', params: {} },
            headers: {},
            clientIp: '127.0.0.1',
            signal: new AbortController().signal,
            processTime: BigInt(0),
        }

        const response = await strategy.dispatch(request)

        console.log(`✅ Success! Successful endpoint won: ${response.headers?.['X-Endpoint']}`)
        console.log(`   Response: ${JSON.stringify(response.body)}\n`)
    } catch (error) {
        console.log(`❌ Test 3 failed: ${error}\n`)
    }

    console.log('🎉 ParallelStrategy testing completed!')
}

// Run the test
testParallelStrategy().catch(console.error)
