import type { EndpointConfig } from '../config/endpoints'
import type { JsonRpcHttpRequestInfo, JsonRpcHttpResponse } from '../modules/jsonrpc-server/types'
import type { SenderResponse } from '../utils/sender/types'
import type { EndpointDispatchStrategy } from './strategies/strategy'
import { highlight, type Logger } from '@kdt310722/logger'
import { JsonRpcError, type JsonRpcResponseMessage } from '@kdt310722/rpc'
import { tap } from '@kdt310722/utils/function'
import { createChildLogger } from '../core/logger'
import { JsonRpcErrorCode } from '../errors/json-rpc-error'
import { Endpoint } from './endpoint'

export interface EndpointManagerOptions {
    defaultStrategy?: string
}

export class EndpointManager {
    protected readonly logger: Logger
    protected readonly strategies: Record<string, EndpointDispatchStrategy>
    protected readonly defaultStrategyName: string

    protected endpoints: Endpoint[]
    protected endpointNameToIndexMap: Record<string, number>

    public constructor(endpoints: EndpointConfig[], strategies: EndpointDispatchStrategy[], { defaultStrategy }: EndpointManagerOptions = {}) {
        this.logger = createChildLogger('endpoint-manager')
        this.endpoints = endpoints.filter((i) => i.enabled).map((i) => this.createEndpoint(i))
        this.endpointNameToIndexMap = this.mapNameToIndex(this.endpoints)
        this.strategies = Object.fromEntries(strategies.map((i) => [i.name, i]))
        this.defaultStrategyName = defaultStrategy ?? Object.keys(this.strategies)[0]

        if (!this.hasStrategy(this.defaultStrategyName)) {
            throw new Error(`Default strategy ${this.defaultStrategyName} not found`)
        }
    }

    public hasEndpoint(name: string) {
        return name in this.endpointNameToIndexMap
    }

    public hasStrategy(name: string) {
        return name in this.strategies
    }

    public getStrategyByName(name: string) {
        return this.strategies[name]
    }

    public getDefaultStrategy() {
        return this.strategies[this.defaultStrategyName]
    }

    public getEndpoints() {
        return this.endpoints
    }

    public getAvailableEndpoints(throwIfEmpty = true) {
        const endpoints = this.endpoints.filter((i) => i.isAvailable())

        if (throwIfEmpty && endpoints.length === 0) {
            throw new JsonRpcError(JsonRpcErrorCode.INTERNAL_ERROR, 'No available endpoints')
        }

        return endpoints
    }

    public getEndpointByName(name: string) {
        return this.endpoints[this.endpointNameToIndexMap[name]]
    }

    public toJsonRpcHttpResponse(response: SenderResponse<JsonRpcResponseMessage>, endpoint?: Endpoint): JsonRpcHttpResponse {
        return { status: response.status.toString(), headers: endpoint ? { 'X-Endpoint': endpoint.name } : {}, body: response.body }
    }

    public async initialize() {
        const timer = tap(this.logger.createTimer(), () => this.logger.info(`Initializing ${highlight(this.endpoints.length)} endpoints...`))

        for (const endpoint of this.endpoints) {
            await endpoint.initialize()
        }

        this.logger.stopTimer(timer, 'info', `All endpoints initialized!`)
        this.logger.info(`Initializing ${highlight(Object.keys(this.strategies).length)} strategies...`)

        const strategyTimer = this.logger.createTimer()

        for (const strategy of Object.values(this.strategies)) {
            await strategy.initialize(this)
        }

        this.logger.stopTimer(strategyTimer, 'info', `All strategies initialized!`)
    }

    public async dispatch(request: JsonRpcHttpRequestInfo): Promise<JsonRpcHttpResponse> {
        const specifiedEndpoint = request.headers['x-endpoint']

        if (specifiedEndpoint && this.hasEndpoint(specifiedEndpoint)) {
            const endpoint = this.getEndpointByName(specifiedEndpoint)

            if (!endpoint.isAvailable()) {
                throw new JsonRpcError(JsonRpcErrorCode.INTERNAL_ERROR, `Endpoint ${specifiedEndpoint} is not available`)
            }

            return endpoint.send(request.message, { signal: request.signal }).then((r) => this.toJsonRpcHttpResponse(r, endpoint))
        }

        const specifiedStrategy = request.headers['x-strategy']

        if (specifiedStrategy && this.hasStrategy(specifiedStrategy)) {
            return this.getStrategyByName(specifiedStrategy).dispatch(request)
        }

        return this.getDefaultStrategy().dispatch(request)
    }

    protected createEndpoint(config: EndpointConfig) {
        return new Endpoint(config)
    }

    protected mapNameToIndex(endpoints: Endpoint[]) {
        return Object.fromEntries(endpoints.map((i, index) => [i.name, index]))
    }
}
