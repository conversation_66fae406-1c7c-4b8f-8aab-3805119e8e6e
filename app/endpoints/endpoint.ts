import { highlight } from '@kdt310722/logger'
import { tap } from '@kdt310722/utils/function'
import { EndpointWithWarmup } from './endpoint-with-warmup'

export class Endpoint extends EndpointWithWarmup {
    public isAvailable() {
        return this.isEnabled && (!this.config.http.maxConcurrentRequests || this.sender.activeRequests < this.config.http.maxConcurrentRequests - 1)
    }

    public async initialize() {
        const timer = tap(this.logger.createTimer(), () => this.logger.info(`Initializing endpoint ${highlight(this.name)}...`))

        await this.enableWarmupIfSupported()

        this.logger.stopTimer(timer, 'info', `Endpoint ${highlight(this.name)} initialized!`)
    }
}
