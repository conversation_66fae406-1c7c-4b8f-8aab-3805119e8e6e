import type { Dispatcher } from 'undici'
import type { EndpointConfig } from '../config/endpoints'
import type { SenderRequestError } from '../utils/sender/errors/sender-request-error'
import type { SenderRequest, SenderResponse } from '../utils/sender/types'
import { context, highlight, type Logger, message } from '@kdt310722/logger'
import { isJsonRpcErrorResponseMessage, isJsonRpcMessage, isJsonRpcResponseMessage, type JsonRpcRequestMessage, type JsonRpcResponseMessage } from '@kdt310722/rpc'
import { isNullish, type Nullable } from '@kdt310722/utils/common'
import { Emitter } from '@kdt310722/utils/event'
import { transform, tryCatch } from '@kdt310722/utils/function'
import { format, formatNanoseconds } from '@kdt310722/utils/number'
import { omit } from '@kdt310722/utils/object'
import { formatDate } from '@kdt310722/utils/time'
import isNetworkError from 'is-network-error'
import { createChildLogger } from '../core/logger'
import { HttpError } from '../errors/http-error'
import { JsonRpcError } from '../errors/json-rpc-error'
import { Limiter } from '../utils/limiter/limiter'
import { Sender, type SenderSendOptions } from '../utils/sender/sender'
import { WARMUP_REQUEST } from './constants'
import { ParseResponseBodyError } from './errors/parse-response-body-error'
import { EndpointMonitor } from './monitor'

export type EndpointSendOptions = Omit<SenderSendOptions, 'method'>

export type BaseEndpointEvents = {
    enable: () => void
    disable: () => void
}

export class BaseEndpoint extends Emitter<BaseEndpointEvents, true> {
    public readonly name: string
    public readonly sender: Sender<JsonRpcResponseMessage>
    public readonly monitor = new EndpointMonitor()

    protected readonly logger: Logger
    protected readonly limiter: Limiter

    #isEnabled: boolean

    public constructor(protected readonly config: EndpointConfig) {
        super()

        if (isNullish(config.http.maxConcurrentRequests)) {
            this.config.http.maxConcurrentRequests = config.http.maxRequestsPerSecond
        }

        this.#isEnabled = config.enabled
        this.name = config.name
        this.logger = createChildLogger(`endpoints:${config.name}`)
        this.sender = this.createSender(config.http)
        this.limiter = this.createLimiter(config.http)
    }

    public get isEnabled() {
        return this.#isEnabled && !this.limiter.isLimited
    }

    public enable() {
        this.#isEnabled = true
        this.emit('enable')
    }

    public disable() {
        this.#isEnabled = false
        this.emit('disable')
    }

    public async send(request: JsonRpcRequestMessage, options?: EndpointSendOptions) {
        return this.sendRawWithLimiter(JSON.stringify(request), { ...options, method: 'POST', headers: { 'Content-Type': 'application/json', ...options?.headers } })
    }

    public async sendRawWithLimiter(body?: Nullable<string>, options?: SenderSendOptions) {
        if (!this.#isEnabled) {
            throw new Error('Endpoint is disabled')
        }

        return this.limiter.schedule(async () => this.sender.send(body, options))
    }

    protected createSender({ retry, ...config }: EndpointConfig['http']) {
        const retryAdditionalOptions = {
            onFailedAttempt: this.handleRetryFailedAttempt.bind(this),
            onResponseAttempt: this.handleRetryFailedAttempt.bind(this),
            shouldRetry: this.shouldRetryRequest.bind(this),
            shouldRetryOnResponse: this.shouldRetryResponse.bind(this),
        }

        const sender = new Sender(config.url, { ...config, responseBodyFormatter: this.formatResponseBody.bind(this), retry: { ...retry, ...retryAdditionalOptions } })

        sender.on('activeRequests', this.handleActiveRequests.bind(this))
        sender.on('connections', this.handleConnections.bind(this))
        sender.on('request', this.handleRequest.bind(this))
        sender.on('response', this.handleResponse.bind(this))
        sender.on('error', this.handleRequestError.bind(this))

        return sender
    }

    protected shouldRetryResponse(response: SenderResponse<JsonRpcResponseMessage>) {
        if (response.metadata[WARMUP_REQUEST]) {
            return false
        }

        return this.isEnabled && isJsonRpcErrorResponseMessage(response.body) && !JsonRpcError.fromErrorObject(response.body.error).isClientError()
    }

    protected shouldRetryRequest(error: unknown) {
        return this.isEnabled && !isNetworkError(error) && !(error instanceof ParseResponseBodyError || error instanceof HttpError)
    }

    protected formatResponseBody(body: string, response: Dispatcher.ResponseData, request: SenderRequest) {
        if (request.metadata[WARMUP_REQUEST]) {
            return null as unknown as JsonRpcResponseMessage
        }

        if (response.statusCode !== 200) {
            throw new HttpError('Request failed').withStatusCode(response.statusCode).withResponse({ ...response, body })
        }

        const data = tryCatch(() => JSON.parse(body), body)

        if (!isJsonRpcMessage(data)) {
            throw new ParseResponseBodyError('Invalid response: Response is not a JSON-RPC message').withBody(body)
        }

        if (!isJsonRpcResponseMessage(data)) {
            throw new ParseResponseBodyError('Invalid response: Response is not a JSON-RPC response').withBody(body)
        }

        return data
    }

    protected handleRetryFailedAttempt(error: unknown, attempts: number, retriesLeft: number) {
        this.monitor.increaseFailedRequest()
        this.logger.warn(`Request to endpoint ${highlight(this.name)} failed, retrying (attempt: ${highlight(attempts)}, retries left: ${highlight(retriesLeft)})...`, error)
    }

    protected createLimiter({ maxRequestsPerSecond }: EndpointConfig['http']) {
        const limiter = new Limiter({ maxRequestsPerSecond })

        limiter.on('limit', this.handleRateLimit.bind(this))
        limiter.on('error', this.handleLimiterError.bind(this))

        return limiter
    }

    protected handleRateLimit(until: Date) {
        this.monitor.increaseRateLimitReached()
        this.logger.warn(`Rate limit reached for endpoint ${highlight(this.name)} until ${highlight(formatDate(until, true))}`)
        this.disable()

        setTimeout(() => this.enable(), until.getTime() - Date.now())
    }

    protected handleLimiterError(error: unknown) {
        this.logger.error(`Error occurred in limiter for endpoint ${highlight(this.name)}`, error)
    }

    protected handleActiveRequests(count: number) {
        this.logger.debug(message(() => `Active requests count for endpoint ${highlight(this.name)}: ${highlight(format(count))}`))
    }

    protected handleConnections(count: number) {
        this.logger.debug(message(() => `Connections count for endpoint ${highlight(this.name)}: ${highlight(format(count))}`))
    }

    protected handleRequest(request: SenderRequest) {
        if (request.metadata[WARMUP_REQUEST]) {
            return
        }

        this.logger.debug(message(() => `Sending request to endpoint ${highlight(this.name)}`), context(() => [this.config.logger.showFullRequest ? request : omit(request, 'headers', 'body')]))
    }

    protected handleResponse(response: SenderResponse<unknown>) {
        this.monitor.increaseSuccessRequest(response.took)

        if (response.metadata[WARMUP_REQUEST]) {
            return
        }

        this.logger.debug(message(() => `Response received from endpoint ${highlight(this.name)}`), context(() => [transform({ ...response, took: formatNanoseconds(response.took) }, (r) => (this.config.logger.showFullResponse ? r : omit(r, 'headers', 'body')))]))
    }

    protected handleRequestError(error: SenderRequestError) {
        this.monitor.increaseFailedRequest()
        this.logger.debug(message(() => `Request to endpoint ${highlight(this.name)} failed`), error)
    }
}
