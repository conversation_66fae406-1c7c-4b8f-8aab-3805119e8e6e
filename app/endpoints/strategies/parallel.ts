import type { JsonRpcHttpRequestInfo, JsonRpcHttpResponse } from '../../modules/jsonrpc-server/types'
import type { SenderResponse } from '../../utils/sender/types'
import type { Endpoint } from '../endpoint'
import { highlight } from '@kdt310722/logger'
import { isJsonRpcErrorResponseMessage, type JsonRpcResponseMessage } from '@kdt310722/rpc'
import { isAbortError } from '@kdt310722/utils/error'
import { JsonRpcError, JsonRpcErrorCode } from '../../errors/json-rpc-error'
import { EndpointDispatchStrategy } from './strategy'

interface EndpointResult {
    endpoint: Endpoint
    result: 'success' | 'error' | 'aborted'
    response?: SenderResponse<JsonRpcResponseMessage>
    error?: unknown
    took?: bigint
}

export class ParallelStrategy extends EndpointDispatchStrategy {
    public async dispatch(request: JsonRpcHttpRequestInfo): Promise<JsonRpcHttpResponse> {
        const availableEndpoints = this.getAvailableEndpoints()

        if (availableEndpoints.length === 0) {
            throw new JsonRpcError(JsonRpcErrorCode.INTERNAL_ERROR, 'No available endpoints')
        }

        this.logger.debug(`Dispatching request to ${highlight(availableEndpoints.length)} endpoints in parallel`)

        // Create internal abort controller for cancelling remaining requests
        const internalAbortController = new AbortController()

        // Combine external and internal abort signals
        const combinedSignal = AbortSignal.any([
            request.signal,
            internalAbortController.signal,
        ])

        try {
            // Send requests to all endpoints in parallel
            const endpointPromises = availableEndpoints.map((endpoint) => this.sendToEndpoint(endpoint, request, combinedSignal))

            // Use Promise.allSettled to handle all requests regardless of individual failures
            const settledResults = await Promise.allSettled(endpointPromises)

            // Process results and separate success from failures
            const results = this.processSettledResults(availableEndpoints, settledResults)
            const successResults = results.filter((r) => r.result === 'success')
            const errorResults = results.filter((r) => r.result === 'error')
            const abortedResults = results.filter((r) => r.result === 'aborted')

            this.logger.debug(`Results: ${highlight(successResults.length)} success, ${highlight(errorResults.length)} errors, ${highlight(abortedResults.length)} aborted`)

            // If we have at least one success, return the fastest one
            if (successResults.length > 0) {
                // Cancel remaining requests since we have a success
                internalAbortController.abort()

                // Find the fastest successful response
                const fastestResult = this.findFastestResult(successResults)

                this.logger.debug(`Returning fastest response from endpoint ${highlight(fastestResult.endpoint.name)} (took: ${fastestResult.took}ns)`)

                return this.toJsonRpcHttpResponse(fastestResult.endpoint, fastestResult.response!)
            }

            // All requests failed - aggregate errors and throw
            this.handleAllFailures(results)
        } catch (error) {
            // Cancel any remaining requests
            internalAbortController.abort()
            throw error
        }
    }

    protected getAvailableEndpoints(): Endpoint[] {
        return this.manager.getEndpoints().filter((endpoint) => endpoint.isAvailable())
    }

    protected async sendToEndpoint(
        endpoint: Endpoint,
        request: JsonRpcHttpRequestInfo,
        signal: AbortSignal,
    ): Promise<EndpointResult> {
        const startTime = process.hrtime.bigint()

        try {
            const response = await endpoint.send(request.message, { signal })
            const took = process.hrtime.bigint() - startTime

            // Check if response contains JsonRPC error
            if (isJsonRpcErrorResponseMessage(response.body)) {
                return {
                    endpoint,
                    result: 'error',
                    response,
                    error: new JsonRpcError(response.body.error.code, response.body.error.message, { data: response.body.error.data }),
                    took,
                }
            }

            return {
                endpoint,
                result: 'success',
                response,
                took,
            }
        } catch (error) {
            const took = process.hrtime.bigint() - startTime

            if (isAbortError(error)) {
                return {
                    endpoint,
                    result: 'aborted',
                    error,
                    took,
                }
            }

            return {
                endpoint,
                result: 'error',
                error,
                took,
            }
        }
    }

    protected processSettledResults(
        endpoints: Endpoint[],
        settledResults: Array<PromiseSettledResult<EndpointResult>>,
    ): EndpointResult[] {
        return settledResults.map((result, index) => {
            const endpoint = endpoints[index]

            if (result.status === 'fulfilled') {
                return result.value
            }

            // Promise was rejected (shouldn't happen with our error handling, but just in case)
            return {
                endpoint,
                result: 'error',
                error: result.reason,
            }
        })
    }

    protected findFastestResult(successResults: EndpointResult[]): EndpointResult {
        return successResults.reduce((fastest, current) => {
            if (!fastest.took || !current.took) {
                return current
            }

            return current.took < fastest.took ? current : fastest
        }, successResults[0])
    }

    protected handleAllFailures(results: EndpointResult[]): never {
        const errorMessages: string[] = []
        const endpointErrors: Record<string, unknown> = {}

        for (const result of results) {
            const endpointName = result.endpoint.name

            if (result.error) {
                endpointErrors[endpointName] = result.error

                if (result.error instanceof Error) {
                    errorMessages.push(`${endpointName}: ${result.error.message}`)
                } else {
                    errorMessages.push(`${endpointName}: ${String(result.error)}`)
                }
            } else {
                errorMessages.push(`${endpointName}: Unknown error`)
            }
        }

        const aggregatedMessage = `All endpoints failed: ${errorMessages.join('; ')}`

        this.logger.error(`Parallel strategy failed: ${aggregatedMessage}`)

        throw new JsonRpcError(
            JsonRpcErrorCode.INTERNAL_ERROR,
            'All endpoints failed',
            {
                data: {
                    strategy: 'parallel',
                    endpointErrors,
                    totalEndpoints: results.length,
                    message: aggregatedMessage,
                },
            },
        )
    }

    protected toJsonRpcHttpResponse(endpoint: Endpoint, response: SenderResponse<JsonRpcResponseMessage>): JsonRpcHttpResponse {
        return {
            status: response.status.toString(),
            headers: { 'X-Endpoint': endpoint.name },
            body: response.body,
        }
    }
}
