import type { JsonRpcHttpRequestInfo, JsonRpcHttpResponse } from '../../modules/jsonrpc-server/types'
import type { Endpoint } from '../endpoint'
import { highlight } from '@kdt310722/logger'
import { isJsonRpcErrorResponseMessage } from '@kdt310722/rpc'
import { isTrueLike } from '@kdt310722/utils/common'
import { isAbortError } from '@kdt310722/utils/error'
import { tap } from '@kdt310722/utils/function'
import { createDeferred, type DeferredPromise } from '@kdt310722/utils/promise'
import { AggregateJsonRpcError } from '../../errors/aggregate-json-rpc-error'
import { JsonRpcError, JsonRpcErrorCode } from '../../errors/json-rpc-error'
import { EndpointDispatchStrategy } from './strategy'

export interface ParallelStrategyDispatchOptions {
    waitForAll?: boolean
}

export class ParallelStrategy extends EndpointDispatchStrategy {
    public async dispatch(request: JsonRpcHttpRequestInfo, { waitForAll = false }: ParallelStrategyDispatchOptions = {}) {
        const endpoints = this.manager.getAvailableEndpoints()
        const timer = tap(this.logger.createTimer(), () => this.logger.debug(`Dispatching request to ${highlight(endpoints.length)} endpoints in parallel...`))

        const abortController = new AbortController()
        const combinedSignal = AbortSignal.any([request.signal, abortController.signal])

        const waitForAll_ = isTrueLike(request.headers['x-wait-for-all'] ?? waitForAll)
        const result = createDeferred<JsonRpcHttpResponse>()
        const errors: Array<{ endpoint: Endpoint, error: JsonRpcError }> = []

        Promise.allSettled(endpoints.map((i) => this.sendRequest(i, result, errors, request, combinedSignal))).then(() => {
            if (!result.isSettled) {
                result.reject(new AggregateJsonRpcError(Object.fromEntries(errors.map((i) => [i.endpoint.name, i.error])), JsonRpcErrorCode.INTERNAL_ERROR, 'All endpoints failed'))
            }
        })

        return result.finally(() => {
            if (!waitForAll_) {
                abortController.abort()
            }

            this.logger.stopTimer(timer, 'debug', `Request dispatched to ${highlight(endpoints.length)} endpoints in parallel!`, { errorsCount: errors.length })
        })
    }

    protected async sendRequest(endpoint: Endpoint, result: DeferredPromise<JsonRpcHttpResponse>, errors: Array<{ endpoint: Endpoint, error: JsonRpcError }>, request: JsonRpcHttpRequestInfo, combinedSignal: AbortSignal) {
        try {
            if (!result.isSettled) {
                const response = await endpoint.send(request.message, { signal: combinedSignal })

                if (isJsonRpcErrorResponseMessage(response.body)) {
                    throw JsonRpcError.fromErrorObject(response.body.error)
                }

                result.resolve(this.manager.toJsonRpcHttpResponse(response, endpoint))
            }
        } catch (error) {
            if (isAbortError(error)) {
                return
            }

            errors.push({ endpoint, error: error instanceof JsonRpcError ? error : new JsonRpcError(JsonRpcErrorCode.INTERNAL_ERROR, 'Internal server error', { cause: error }) })
        }
    }
}
