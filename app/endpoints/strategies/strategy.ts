import type { AnyObject } from '@kdt310722/utils/object'
import type { Awaitable } from '@kdt310722/utils/promise'
import type { JsonRpcHttpRequestInfo, JsonRpcHttpResponse } from '../../modules/jsonrpc-server/types'
import type { EndpointManager } from '../manager'
import { createChildLogger } from '../../core/logger'

export abstract class EndpointDispatchStrategy<TDispatchOptions extends AnyObject = AnyObject> {
    protected manager!: EndpointManager

    public get name() {
        return this.constructor.name
    }

    public get logger() {
        return createChildLogger(`endpoint-manager:strategies:${this.name}`)
    }

    public abstract dispatch(request: JsonRpcHttpRequestInfo, options?: TDispatchOptions): Promise<JsonRpcHttpResponse>

    public initialize(manager: EndpointManager): Awaitable<void> {
        this.manager = manager
    }
}
