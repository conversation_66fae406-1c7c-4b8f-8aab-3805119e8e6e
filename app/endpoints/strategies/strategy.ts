import type { Awaitable } from '@kdt310722/utils/promise'
import type { JsonRpcHttpRequestInfo, JsonRpcHttpResponse } from '../../modules/jsonrpc-server/types'
import type { EndpointManager } from '../manager'
import { createChildLogger } from '../../core/logger'

export abstract class EndpointDispatchStrategy {
    protected manager!: EndpointManager

    public get name() {
        return this.constructor.name
    }

    public get logger() {
        return createChildLogger(`endpoint-manager:strategies:${this.name}`)
    }

    public abstract dispatch(request: JsonRpcHttpRequestInfo): Promise<JsonRpcHttpResponse>

    public initialize(manager: EndpointManager): Awaitable<void> {
        this.manager = manager
    }
}
