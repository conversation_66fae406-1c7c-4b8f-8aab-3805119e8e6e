import { isArray } from '@kdt310722/utils/array'
import { map } from '@kdt310722/utils/object'
import { JsonRpcError } from './json-rpc-error'

export class AggregateJsonRpcError extends JsonRpcError {
    public constructor(public readonly errors: JsonRpcError[] | Record<string, JsonRpcError>, ...args: ConstructorParameters<typeof JsonRpcError>) {
        super(...args)
    }

    public override toErrorObject() {
        const error = super.toErrorObject()
        const errors = isArray(this.errors) ? this.errors.map((i) => i.toErrorObject()) : map(this.errors, (k, v) => [k, v.toErrorObject()])

        error.data ??= {}
        error.data.errors = errors

        return error
    }
}
