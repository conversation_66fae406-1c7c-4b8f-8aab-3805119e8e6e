import { config } from '../config'
import { setJsonRpcRequestHandler } from '../core/server'
import { EndpointManager } from '../endpoints/manager'
import { ParallelStrategy } from '../endpoints/strategies/parallel'

const manager = new EndpointManager(config.endpoints, [
    new ParallelStrategy(),
])

setJsonRpcRequestHandler(async (request) => {
    return manager.dispatch(request)
})

export async function initializeEndpointManager() {
    await manager.initialize()
}
